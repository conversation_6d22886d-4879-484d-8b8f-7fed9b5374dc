import { type MetaFunction } from 'react-router';
import { useEffect } from 'react';

export const meta: MetaFunction = () => {
  return [
    { title: 'Brewing Guides | Big River Coffee' },
    { description: 'Learn how to brew the perfect cup with our detailed guides for various brewing methods. From pour-over to French press, we\'ve got you covered.' }
  ];
};

export default function BrewPage() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    document.body.classList.add('brew');

    return () => {
      document.body.classList.remove('brew');
    };
  }, []);

  return (
    <>
    <style>{`
      body.brew {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.brew main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

    <div style={{
      backgroundColor: '#eeedc1',
      margin: 0,
      padding: 0,
      width: '100vw',
      minHeight: '100vh'
    }}>
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-army-900 text-white">
        <div className="absolute inset-0 z-0 opacity-40">
          <img
            src="/coffeebean.webp"
            alt="Coffee beans background"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative z-10 w-full px-4 sm:px-6 lg:px-8 py-16 sm:py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6" style={{ fontFamily: 'var(--font-title)' }}>The Art of Brewing</h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 sm:mb-8" style={{ fontFamily: 'var(--font-body)' }}>
              Brewing great coffee is both an art and a science. These guides will help you master various brewing methods to bring out the best flavors in your coffee beans.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative overflow-hidden">
        {/* Background decorative circles */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
        <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
        <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
        <div className="absolute bottom-1/4 left-1/3 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
        <div className="absolute top-3/4 right-1/3 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>

        <div className="w-full px-4 sm:px-6 lg:px-8 py-16 relative z-10">
          <div className="w-full">

          {/* Quick Navigation */}
          <div className="mb-16">
            <div className="bg-army-600 rounded-xl p-6 sm:p-8">
              <div className="text-center mb-6">
                <span className="text-army-600 font-semibold text-sm uppercase tracking-wider bg-white px-4 py-1 rounded-full shadow-sm inline-block mb-4" style={{ fontFamily: 'var(--font-header)' }}>Brewing Methods</span>
                <h3 className="text-xl sm:text-2xl font-bold text-white" style={{ fontFamily: 'var(--font-title)' }}>Jump to a Brewing Method</h3>
                <div className="w-24 h-1 bg-white rounded my-4 mx-auto"></div>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-5">
                <a href="#pour-over" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Pour Over</span>
                </a>

                <a href="#french-press" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4h16v7a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 15v5m8-5v5m-4-5v5m-6-16v3m12-3v3" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">French Press</span>
                </a>

                <a href="#aeropress" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 12c-3.5 0-7 1.5-7 4v4h14v-4c0-2.5-3.5-4-7-4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 4v8h4V4m-2 12v4" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">AeroPress</span>
                </a>

                <a href="#espresso" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 11h16m-8-7v14" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7a4 4 0 0 0-4 4v6a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4v-6a4 4 0 0 0-4-4H8Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Espresso</span>
                </a>

                <a href="#cold-brew" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v4m-4 4H4m3 4-2 2m10-2 2 2m-4-6a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 14a5 5 0 0 0 10 0v-4a5 5 0 0 0-10 0v4Z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Cold Brew</span>
                </a>

                <a href="#moka-pot" className="flex flex-col items-center p-3 sm:p-4 rounded-lg hover:bg-army-700 transition-colors">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-white flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-army-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10v10m12-10v10M4 20h16M8 4h8l2 6H6l2-6Z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 10h12" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-white">Moka Pot</span>
                </a>
              </div>
            </div>
          </div>

          {/* Pour Over Section */}
          <section id="pour-over" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/pour_over.webp"
                    alt="Pour Over Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>Pour Over (V60/Chemex)</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Medium Difficulty
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        3-4 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    Pour over brewing highlights the delicate flavors and aromas in your coffee. It's perfect for single-origin beans where you want to taste the distinct characteristics.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>V60 or Chemex brewer</li>
                      <li>Paper filter</li>
                      <li>Gooseneck kettle</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>22g coffee (medium-fine grind)</li>
                      <li>360g water (205°F / 96°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Place the filter in the dripper and rinse with hot water to remove paper taste and preheat the brewer</li>
                      <li>Add ground coffee and level the bed</li>
                      <li>Start timer and pour 50g of water for the bloom, making sure all grounds are saturated</li>
                      <li>After 30 seconds, slowly pour water in circular motions until you reach 150g total</li>
                      <li>Continue pouring in stages until you reach 360g total water</li>
                      <li>Total brew time should be 3-4 minutes</li>
                      <li>Enjoy your perfectly extracted pour over coffee!</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* French Press Section */}
          <section id="french-press" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/french_press.webp"
                    alt="French Press Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>French Press</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        4 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    French press brewing creates a full-bodied, rich cup of coffee. The metal filter allows oils and fine particles through, resulting in a robust flavor profile.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>French press</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                      <li>Stirring spoon</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>30g coffee (coarse grind)</li>
                      <li>500g water (200°F / 93°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Preheat the French press with hot water, then empty</li>
                      <li>Add coarsely ground coffee to the press</li>
                      <li>Pour hot water over the coffee, ensuring all grounds are saturated</li>
                      <li>Stir gently and place the lid on top (don't press down yet)</li>
                      <li>Let steep for 4 minutes</li>
                      <li>Slowly press the plunger down</li>
                      <li>Serve immediately to avoid over-extraction</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* AeroPress Section */}
          <section id="aeropress" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/aeropress.webp"
                    alt="AeroPress Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>AeroPress</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        2 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    The AeroPress combines immersion and pressure brewing for a clean, smooth cup. It's portable, quick, and produces consistently great coffee.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>AeroPress</li>
                      <li>AeroPress filter</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Timer</li>
                      <li>Stirring paddle</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>17g coffee (medium-fine grind)</li>
                      <li>250g water (185°F / 85°C)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Insert filter into cap and rinse with hot water</li>
                      <li>Assemble AeroPress in inverted position</li>
                      <li>Add ground coffee and pour water, saturating all grounds</li>
                      <li>Stir gently for 10 seconds</li>
                      <li>Let steep for 1 minute</li>
                      <li>Attach cap and flip onto cup</li>
                      <li>Press down steadily for 30 seconds</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Espresso Section */}
          <section id="espresso" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/brewing_3.webp"
                    alt="Espresso Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>Espresso</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Advanced
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        25-30 sec brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    Espresso is the foundation of many coffee drinks. It requires precision and practice to master, but produces an intense, concentrated shot of coffee.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>Espresso machine</li>
                      <li>Portafilter and basket</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                      <li>Tamper</li>
                      <li>Timer</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>18-20g coffee (fine grind)</li>
                      <li>36-40g liquid output</li>
                      <li>Water temperature: 200°F / 93°C</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Preheat the espresso machine and portafilter</li>
                      <li>Grind coffee beans to a fine consistency</li>
                      <li>Dose 18-20g of ground coffee into the portafilter</li>
                      <li>Level and tamp the coffee with 30lbs of pressure</li>
                      <li>Lock the portafilter into the machine</li>
                      <li>Start extraction, aiming for 25-30 seconds</li>
                      <li>Stop when you reach 36-40g of liquid output</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Cold Brew Section */}
          <section id="cold-brew" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/cold_brew.webp"
                    alt="Cold Brew Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>Cold Brew</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Easy
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        12-24 hr brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    Cold brew produces a smooth, less acidic coffee concentrate. It's perfect for hot summer days and can be stored in the refrigerator for up to a week.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>Large jar or cold brew maker</li>
                      <li>Fine mesh strainer or cheesecloth</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>100g coffee (coarse grind)</li>
                      <li>1000g cold water</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Coarsely grind your coffee beans</li>
                      <li>Combine coffee and cold water in a large jar</li>
                      <li>Stir to ensure all grounds are saturated</li>
                      <li>Cover and let steep for 12-24 hours at room temperature</li>
                      <li>Strain through fine mesh or cheesecloth</li>
                      <li>Dilute concentrate with water or milk to taste</li>
                      <li>Serve over ice and enjoy</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Moka Pot Section */}
          <section id="moka-pot" className="mb-24">
            <div className="bg-army-600 rounded-xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-2/5">
                  <img
                    src="/mokapot_new.webp"
                    alt="Moka Pot Coffee"
                    className="h-64 md:h-full w-full object-cover"
                  />
                </div>
                <div className="p-6 md:p-8 md:w-3/5">
                  <div className="flex flex-col sm:flex-row sm:items-center mb-4">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2 sm:mb-0" style={{ fontFamily: 'var(--font-title)' }}>Moka Pot</h2>
                    <div className="sm:ml-auto flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-army-600">
                        Medium
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-500 text-white">
                        5-10 min brew
                      </span>
                    </div>
                  </div>

                  <p className="text-white mb-6">
                    The Moka pot, also known as a stovetop espresso maker, produces a strong, concentrated coffee with a distinctive flavor. It's a classic Italian brewing method.
                  </p>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">You'll Need:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>Moka pot</li>
                      <li>Stovetop or heat source</li>
                      <li>Digital scale</li>
                      <li>Freshly roasted coffee beans</li>
                      <li>Burr grinder</li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-2">Ingredients:</h3>
                    <ul className="list-disc pl-5 text-white">
                      <li>20-22g coffee (medium-fine grind)</li>
                      <li>Hot water (just below boiling)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">Instructions:</h3>
                    <ol className="list-decimal pl-5 text-white">
                      <li>Fill the bottom chamber with hot water up to the safety valve</li>
                      <li>Insert the filter basket and fill with ground coffee</li>
                      <li>Level the coffee but don't tamp</li>
                      <li>Screw the top and bottom chambers together</li>
                      <li>Place on medium heat</li>
                      <li>When coffee starts gurgling, remove from heat</li>
                      <li>Serve immediately for best flavor</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <div className="text-center py-16">
            <h2 className="text-3xl font-bold text-white mb-4" style={{ fontFamily: 'var(--font-title)' }}>Ready to Brew?</h2>
            <p className="text-xl text-white mb-8" style={{ fontFamily: 'var(--font-body)' }}>Get our premium coffee beans delivered to your door</p>
            <a
              href="/collections/all"
              className="inline-flex items-center px-8 py-4 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors duration-200"
              style={{ color: 'white' }}
            >
              <span className="text-white">Try Our Coffee</span>
              <svg className="w-5 h-5 ml-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
